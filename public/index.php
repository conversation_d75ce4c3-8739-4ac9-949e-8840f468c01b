<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../db/db.php';

session_start();

// Route-Logik
$route = $_GET['route'] ?? 'welcome';

switch ($route) {

    // Startseite für nicht eingeloggte User 
    case '':
    case 'welcome':
        require __DIR__ . '/../views/welcome.php';
        break;

    case 'oauth-callback':
        require __DIR__ . '/oauth_callback_handler.php';
        break;

    case 'api-test':
	require __DIR__ . '/../views/api_test.php';
	break;

    // Dashboard – geschützt
    case 'dashboard':
        if (!isset($_SESSION['organization_id'])) {
            header("Location: /welcome");
            exit;
        }
        require __DIR__ . '/../views/dashboard.php';
        break;

    // Rechnungsvorlagen – geschützt
    case 'invoice-templates':
        if (!isset($_SESSION['organization_id'])) {
            header("Location: /welcome");
            exit;
        }
        require __DIR__ . '/../views/invoice_templates.php';
        break;

    // Einstellungen – geschützt
    case 'settings':
        if (!isset($_SESSION['organization_id'])) {
            header("Location: /welcome");
            exit;
        }
        require __DIR__ . '/../views/settings.php';
        break;

    // Login-Weiterleitung zu OAuth
    case 'login':
        require __DIR__ . '/login_handler.php';
        break;

    // Logout + Redirect zu Startseite
    case 'logout':
        session_destroy();
        header("Location: /welcome");
        break;

    // 404-Fallback
    default:
        http_response_code(404);
        echo "<h1>404 – Seite nicht gefunden</h1>";
        break;
}

