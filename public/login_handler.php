<?php
use <PERSON><PERSON>jett\OpenIDConnectClient;
require_once __DIR__ . '/../vendor/autoload.php';
session_start();

$oidc = new OpenIDConnectClient(
    'https://auth.bexio.com/realms/bexio',
    'fb7bcebd-1402-4791-b27d-e5f742f01490',
    'AO8Wr1q37jA82R8DMpcpejj_8x49t81RBW5lWGGKawvIBxkZ7eBC7Ixx4LyiS92D7VGdDiA57ASqowoF2AcKnJ4'
);
//$oidc->setRedirectURL('https://php.rebill.ch/oauth-callback');
$oidc->setRedirectURL('http://localhost:9000/oauth-callback');
$oidc->addScope('openid profile email company_profile offline_access kb_invoice_edit contact_edit');
$oidc->providerConfigParam([
    'authorization_endpoint' => 'https://auth.bexio.com/realms/bexio/protocol/openid-connect/auth',
    'token_endpoint'         => 'https://auth.bexio.com/realms/bexio/protocol/openid-connect/token',
    'userinfo_endpoint'      => 'https://auth.bexio.com/realms/bexio/protocol/openid-connect/userinfo',
]);
$oidc->authenticate();
?>