<?php
require_once __DIR__ . '/../middleware/auth.php';
$user = requireLoginAndFreshToken();

function fetchBexio($endpoint, $token) {
    $ch = curl_init("https://api.bexio.com/$endpoint");
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => [
            "Authorization: Bearer $token",
            "Accept: application/json"
        ]
    ]);
    $result = curl_exec($ch);
    curl_close($ch);
    return json_decode($result, true);
}

$contacts = fetchBexio("3.0/contact", $user['access_token']);
$units = fetchBexio("2.0/units", $user['access_token']);
$taxes = fetchBexio("2.0/tax", $user['access_token']);

$response = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Save recurring_template (nur als Vorlage)
    global $pdo;
    $org_id = $user['organization_id'];
    $contact_id = $_POST['contact_id'];
    $title = $_POST['title'];
    $start_date = $_POST['start_date'];
    $interval_str = $_POST['interval_str'];
    $positions = json_encode($_POST['positions']);

    $stmt = $pdo->prepare("INSERT INTO recurring_templates (organization_id, contact_id, title, positions, start_date, interval_str) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->execute([$org_id, $contact_id, $title, $positions, $start_date, $interval_str]);
    $response = "Vorlage gespeichert. Die erste Rechnung wird automatisch zum gewünschten Zeitpunkt erstellt.";
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Neue Rechnung</title>
    <style>
    body { font-family: sans-serif; margin: 2em; }
    input, select { padding: 5px; margin-bottom: 10px; display: block; width: 100%; }
    label { font-weight: bold; margin-top: 1em; display: block; }
    button { padding: 10px 20px; }
    fieldset { margin-bottom: 1em; padding: 1em; background: #f7f7f7; }
    </style>
    <script>
    let units = <?= json_encode($units) ?>;
    let taxes = <?= json_encode($taxes) ?>;
    function addPosition() {
        const container = document.getElementById('positions');
        const index = container.children.length;
        let unitOptions = units.map(u => `<option value="${u.id}">${u.name}</option>`).join('');
        let taxOptions = taxes.map(t => `<option value="${t.id}">${t.name}</option>`).join('');
        const div = document.createElement('div');
        div.innerHTML = `
        <fieldset>
            <legend>Position ${index + 1}</legend>
            <label>Text <input name="positions[${index}][text]" required></label>
            <small>Platzhalter: {'{monat}'}, {'{jahr}'}, {'{nächstesjahr}'}, {'{nächstermonat}'}</small>
            <label>Menge <input type="number" step="0.01" name="positions[${index}][qty]" required></label>
            <label>Preis <input type="number" step="0.01" name="positions[${index}][price]" required></label>
            <label>Einheit <select name="positions[${index}][unit_id]">${unitOptions}</select></label>
            <label>MWSt <select name="positions[${index}][tax_id]">${taxOptions}</select></label>
        </fieldset>
        `;
        container.appendChild(div);
    }
    </script>
</head>
<body>
<h1>Neue wiederkehrende Rechnung</h1>
<form method="post">
  <label>Kunde:</label>
  <select name="contact_id" required>
    <?php foreach ($contacts as $c): ?>
      <option value="<?= $c['id'] ?>"><?= htmlspecialchars($c['name_1'] ?? '') ?> <?= htmlspecialchars($c['name_2'] ?? '') ?></option>
    <?php endforeach; ?>
  </select>
  <label>Titel der Rechnung:</label>
  <input name="title" required>
  <label>Startdatum (Rechnungsdatum):</label>
  <input name="start_date" type="date" required>
  <label>Intervall:</label>
  <select name="interval_str" required>
    <option value="+1 month">monatlich</option>
    <option value="+3 months">vierteljährlich</option>
    <option value="+6 months">halbjährlich</option>
    <option value="+1 year">jährlich</option>
  </select>
  <div id="positions"></div>
  <button type="button" onclick="addPosition()">+ Position hinzufügen</button><br><br>
  <button type="submit">Vorlage speichern</button>
</form>
<?php if ($response): ?>
<h2><?= $response ?></h2>
<?php endif; ?>
<p><a href="/dashboard.php">Zurück</a></p>
</body>
</html>