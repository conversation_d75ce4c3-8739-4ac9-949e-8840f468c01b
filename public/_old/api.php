<?php
require_once __DIR__ . '/../controllers/BexioController.php';

$request_uri = $_SERVER['REQUEST_URI'];
$method = $_SERVER['REQUEST_METHOD'];

switch ($request_uri) {
    case '/api/get-business':
        if ($method === 'POST') getBusinessData();
        break;
    case '/api/get-contacts':
        if ($method === 'POST') getContacts();
        break;
    case '/api/get-contact':
        if ($method === 'POST') getContact();
        break;
    case '/api/get-units':
        if ($method === 'POST') getUnits();
        break;
    case '/api/get-invoices':
        if ($method === 'POST') getInvoices();
        break;
    case '/api/get-taxes':
        if ($method === 'POST') getTaxes();
        break;
    case '/api/get-currencies':
        if ($method === 'POST') getCurrencies();
        break;
    case '/api/create-customer':
        if ($method === 'POST') createCustomer();
        break;
    case '/api/create-invoice':
        if ($method === 'POST') createInvoice();
        break;
    case '/api/update-invoice':
        if ($method === 'POST') updateInvoice();
        break;
    case '/api/start-invoice':
        if ($method === 'POST') restartRecurringBilling();
        break;
    case '/api/delete-invoice':
        if ($method === 'POST') deleteRecurringBilling();
        break;
    case '/api/pause-invoice':
        if ($method === 'POST') pauseRecurringBilling();
        break;
    default:
        http_response_code(404);
        echo json_encode(["error" => "Unknown endpoint"]);
}
?>