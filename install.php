<?php
require_once __DIR__ . '/db/db.php';

$queries = [
    "CREATE TABLE organizations (
      id INT AUTO_INCREMENT PRIMARY KEY,
      name VARCHAR(255),
      bexio_org_id VARCHAR(255) UNIQUE,
      subscription_start DATE,
      subscription_status ENUM('trial', 'active', 'inactive') DEFAULT 'trial',
      subscription_model ENUM('monthly', 'yearly') DEFAULT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )",
    "CREATE TABLE users (
      id INT AUTO_INCREMENT PRIMARY KEY,
      organization_id INT,
      bexio_id VARCHAR(255),
      access_token TEXT,
      refresh_token TEXT,
      token_expires_at DATETIME,
      refresh_token_rotated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      name VARCHAR(255),
      email VARCHAR(255),
      is_admin BOOLEAN DEFAULT 0,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (organization_id) REFERENCES organizations(id)
    )",
    "CREATE TABLE recurring_templates (
      id INT AUTO_INCREMENT PRIMARY KEY,
      organization_id INT NOT NULL,
      contact_id INT NOT NULL,
      title VARCHAR(255),
      positions JSON,
      start_date DATE NOT NULL,
      interval_str VARCHAR(20) NOT NULL,
      last_executed DATE DEFAULT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (organization_id) REFERENCES organizations(id)
    )"
];

foreach ($queries as $sql) {
    try {
        $pdo->exec($sql);
        echo "Tabelle erfolgreich erstellt oder vorhanden.<br>";
    } catch (PDOException $e) {
        echo "Fehler: " . $e->getMessage() . "<br>";
    }
}
?>