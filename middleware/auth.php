<?php
require_once __DIR__ . '/../vendor/autoload.php';
use <PERSON><PERSON>jett\OpenIDConnectClient;

session_start();
require_once __DIR__ . '/../db/db.php';

if (!isset($_SESSION['user_id'])) {
    header('Location: /login');
    exit;
}

$stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

if (!$user) {
    session_destroy();
    header('Location: /login');
    exit;
}

$now = time();
$tokenExpires = isset($user['token_expires_at']) ? strtotime($user['token_expires_at']) : 0;
$refreshToken = isset($user['refresh_token']) ? $user['refresh_token'] : '';

if ($refreshToken && $now > $tokenExpires - 300) {
    $oidc = new OpenIDConnectClient(
        'https://auth.bexio.com/realms/bexio',
    	'fb7bcebd-1402-4791-b27d-e5f742f01490',
    	'AO8Wr1q37jA82R8DMpcpejj_8x49t81RBW5lWGGKawvIBxkZ7eBC7Ixx4LyiS92D7VGdDiA57ASqowoF2AcKnJ4'
    );
    $oidc->providerConfigParam([
        'token_endpoint' => 'https://auth.bexio.com/realms/bexio/protocol/openid-connect/token',
    ]);

    try {
        $oidc->refreshToken($refreshToken);
        $newAccessToken = $oidc->getAccessToken();
        $newRefreshToken = $oidc->getRefreshToken();
        $expiresIn = 3600; // bexio default (1h)
        $newExpires = $now + $expiresIn;

        $_SESSION['access_token'] = $newAccessToken;
        $_SESSION['refresh_token'] = $newRefreshToken;
        $_SESSION['token_expires_at'] = date('Y-m-d H:i:s', $newExpires);

        $stmt = $pdo->prepare("UPDATE users SET access_token = ?, refresh_token = ?, token_expires_at = ? WHERE id = ?");
        $stmt->execute([
            $newAccessToken,
            $newRefreshToken,
            date('Y-m-d H:i:s', $newExpires),
            $user['id']
        ]);

        $user['access_token'] = $newAccessToken;
        $user['refresh_token'] = $newRefreshToken;
        $user['token_expires_at'] = date('Y-m-d H:i:s', $newExpires);

    } catch (Exception $e) {
        session_destroy();
        header('Location: /login?error=token_refresh_failed');
        exit;
    }
}

$accessToken = $user['access_token'];
