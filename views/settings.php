<?php
require_once __DIR__ . '/../db/db.php';
require_once __DIR__ . '/../vendor/autoload.php';
session_start();

$org_id = $_SESSION['organization_id'] ?? null;
if (!$org_id) {
    header("Location: /welcome");
    exit;
}

// bexio Access Token aus DB holen
$stmt = $pdo->prepare("SELECT access_token FROM users WHERE organization_id = ? ORDER BY id ASC LIMIT 1");
$stmt->execute([$org_id]);
$access_token = $stmt->fetchColumn();

// MWST-Typen von bexio laden
$mwst_types = [];
if ($access_token) {
    $ch = curl_init('https://api.bexio.com/3.0/taxes?scope=active&types=sales_tax');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $access_token,
        'Accept: application/json'
    ]);
    $result = curl_exec($ch);
    $http_status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    if ($http_status === 200) {
        $mwst_types = json_decode($result, true) ?? [];
    }
}

// Einstellungen speichern
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $mwst_id = intval($_POST['bexio_mwst_id'] ?? 0);
    $email_tpl = trim($_POST['email_template'] ?? '');

    $stmt = $pdo->prepare("
        INSERT INTO organization_settings (organization_id, language_code, bexio_mwst_id, email_template)
        VALUES (?, 'de', ?, ?)
        ON DUPLICATE KEY UPDATE bexio_mwst_id = VALUES(bexio_mwst_id), email_template = VALUES(email_template)
    ");
    $stmt->execute([$org_id, $mwst_id, $email_tpl]);

    $alert = "Einstellungen gespeichert!";
}

// Aktuelle Settings laden
$stmt = $pdo->prepare("SELECT bexio_mwst_id, email_template FROM organization_settings WHERE organization_id = ? AND language_code = 'de'");
$stmt->execute([$org_id]);
$data = $stmt->fetch() ?: ['bexio_mwst_id' => '', 'email_template' => ''];

$title = "Einstellungen";
ob_start();
?>

<h1 class="mb-4">Einstellungen</h1>

<?php if ($mwst_types === []): ?>
    <div class="alert alert-warning">Die MWST-Sätze konnten nicht von bexio geladen werden.</div>
<?php endif; ?>

<form method="post" class="mb-5" autocomplete="off">
    <div class="mb-3">
        <label class="form-label">Standard MWST-Typ</label>
        <select name="bexio_mwst_id" class="form-select" required>
            <option value="">-- Bitte wählen --</option>
            <?php foreach ($mwst_types as $mwst): ?>
                <option value="<?= $mwst['id'] ?>" <?= ($data['bexio_mwst_id'] == $mwst['id']) ? 'selected' : '' ?>>
                    <?= htmlspecialchars($mwst['display_name']) ?>
                </option>
            <?php endforeach ?>
        </select>
    </div>

    <div class="mb-3">
        <label class="form-label">E-Mail-Template für Rechnungsversand</label>
        <textarea name="email_template" class="form-control" rows="5"><?= htmlspecialchars($data['email_template']) ?></textarea>
        <small class="form-text text-muted">Verfügbare Platzhalter: <code>{rechnung_nummer}</code>, <code>{betrag}</code>, <code>{link}</code></small>
    </div>

    <button type="submit" class="btn btn-primary">Einstellungen speichern</button>
</form>

<?php
$content = ob_get_clean();
require __DIR__ . '/layout.php';
?>
