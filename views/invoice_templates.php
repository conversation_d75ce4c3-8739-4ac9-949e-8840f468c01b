<?php
require_once __DIR__ . '/../db/db.php';
require_once __DIR__ . '/../vendor/autoload.php';
session_start();

$org_id = $_SESSION['organization_id'] ?? null;
if (!$org_id) { header('Location: /login'); exit; }

// Access Token laden (für bexio Kunden & MWST)
$stmt = $pdo->prepare("SELECT access_token FROM users WHERE organization_id = ? LIMIT 1");
$stmt->execute([$org_id]);
$access_token = $stmt->fetchColumn();

function fetch_bexio_data($url, $access_token) {
    $ch = curl_init($ $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $access_token,
        'Accept: application/json'
    ]);
    $response = curl_exec($ch);
    curl_close($ch);
    return json_decode($response, true) ?? [];
}

// Kunden & MWST laden
$contacts = fetch_bexio_data('https://api.bexio.com/3.0/contact', $access_token);
$taxes = fetch_bexio_data('https://api.bexio.com/3.0/taxes?scope=active', $access_token);

$title = "Neue Rechnungsvorlage";
ob_start();
?>

<h1 class="mb-4">Rechnungsvorlage erstellen</h1>
<form method="post" action="/save-invoice-template" id="templateForm">
  <div class="row">
    <div class="col-md-6">
      <div class="mb-3">
        <label class="form-label">Titel in reBill *</label>
        <input type="text" name="title_internal" class="form-control" required>
      </div>
      <div class="mb-3">
        <label class="form-label">Beschreibung</label>
        <textarea name="description_internal" class="form-control"></textarea>
      </div>
      <div class="mb-3">
        <label class="form-label">Titel in bexio *</label>
        <input type="text" name="title_bexio" class="form-control" required>
      </div>
      <div class="mb-3">
        <label class="form-label">Referenz in bexio</label>
        <textarea name="reference_bexio" class="form-control"></textarea>
      </div>
      <div class="mb-3">
        <label class="form-label">Kunde</label>
        <select name="contact_id" class="form-select">
          <option value="">Bitte wählen</option>
          <?php foreach ($contacts as $c): ?>
            <option value="<?= $c['id'] ?>"><?= htmlspecialchars($c['name_1'] . ' ' . $c['name_2']) ?></option>
          <?php endforeach ?>
        </select>
      </div>
      <div class="mb-3">
        <label class="form-label">Intervall *</label>
        <select name="interval_type" class="form-select" required>
          <option value="monatlich">Monatlich</option>
          <option value="vierteljährlich">Vierteljährlich</option>
          <option value="halbjährlich">Halbjährlich</option>
          <option value="jährlich">Jährlich</option>
        </select>
      </div>
      <div class="mb-3">
        <label class="form-label">Startdatum *</label>
        <input type="date" name="invoice_date" class="form-control" required>
      </div>
    </div>
    <div class="col-md-6">
      <h5>Positionen</h5>
      <div id="items-container"></div>
      <button type="button" class="btn btn-outline-secondary btn-sm" onclick="addItem()">+ Position</button>
    </div>
  </div>
  <hr>
  <button type="submit" class="btn btn-primary">Speichern</button>
</form>

<script>
let itemIndex = 0;
function addItem() {
  const container = document.getElementById('items-container');
  const item = document.createElement('div');
  item.className = 'border rounded p-2 mb-2';
  item.innerHTML = `
    <div class="row g-2 align-items-end">
      <div class="col-md-4">
        <label class="form-label">Name</label>
        <input type="text" name="items[\${itemIndex}][name]" class="form-control">
      </div>
      <div class="col-md-2">
        <label class="form-label">Menge</label>
        <input type="number" step="0.01" name="items[\${itemIndex}][quantity]" class="form-control">
      </div>
      <div class="col-md-2">
        <label class="form-label">Einheit</label>
        <input type="text" name="items[\${itemIndex}][unit]" class="form-control">
      </div>
      <div class="col-md-2">
        <label class="form-label">Preis</label>
        <input type="number" step="0.01" name="items[\${itemIndex}][price]" class="form-control">
      </div>
      <div class="col-md-2">
        <label class="form-label">MWST</label>
        <select name="items[\${itemIndex}][tax_id]" class="form-select">
          <option value="">–</option>
          <?php foreach ($taxes as $t): ?>
            <option value="<?= $t['id'] ?>"><?= htmlspecialchars($t['name']) ?> (<?= $t['value'] ?>%)</option>
          <?php endforeach ?>
        </select>
      </div>
    </div>
  `;
  container.appendChild(item);
  itemIndex++;
}
</script>

<?php
$content = ob_get_clean();
require __DIR__ . '/layout.php';
?>
