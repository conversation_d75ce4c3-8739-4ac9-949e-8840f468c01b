<?php
require_once __DIR__ . '/../db/db.php';
session_start();

$org_logo = null;
$org_name = 'einfach wiederkehrende Rechnungen';

if (isset($_SESSION['organization_id'])) {
    $stmt = $pdo->prepare("SELECT bexio_org_id, name FROM organizations WHERE id = ?");
    $stmt->execute([$_SESSION['organization_id']]);
    if ($org = $stmt->fetch()) {
        $bexio_org_id = $org['bexio_org_id']; // <- das ist jetzt der Platzhalter für den Logo-Ordner!
        $org_name = $org['name'];
    }
}

$logo_url = "https://office.bexio.com/img/".$bexio_org_id."/companyLogo.png";
if($bexio_org_id == null)
	{$logo_url = "https://php.rebill.ch/assets/reBill.png";}

function navactive($r) {
    return (isset($_GET['route']) && $_GET['route'] === $r) ? 'active' : '';
}
?>
<!doctype html>
<html lang="de">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?= htmlspecialchars($title ?? 'rebill') ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light bg-light mb-4">
      <div class="container">
        <a class="navbar-brand d-flex align-items-center" href="/dashboard">
            <img src="<?= htmlspecialchars($logo_url) ?>" alt="Logo" style="height:32px;width:auto;margin-right:12px;" />
          <span><?= htmlspecialchars($org_name) ?></span>
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainnav">
          <span class="navbar-toggler-icon"></span>
        </button>
<?php
$route = $_GET['route'] ?? 'welcome';
if ($route !== 'welcome'):
?>
    <div class="collapse navbar-collapse" id="mainnav">
        <ul class="navbar-nav ms-auto">
            <li class="nav-item"><a class="nav-link <?= navactive('dashboard') ?>" href="/dashboard">Dashboard</a></li>
            <li class="nav-item"><a class="nav-link <?= navactive('invoice-templates') ?>" href="/invoice-templates">Rechnungsvorlagen</a></li>
            <li class="nav-item"><a class="nav-link <?= navactive('settings') ?>" href="/settings">Einstellungen</a></li>
            <li class="nav-item"><a class="btn btn-outline-danger" href="/logout">Logout</a></li>
        </ul>
    </div>
<?php endif; ?>

      </div>
    </nav>
    <div class="container">
      <?php if (isset($alert)) { ?>
        <div class="alert alert-info"><?= htmlspecialchars($alert) ?></div>
      <?php } ?>
      <?= $content ?>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
