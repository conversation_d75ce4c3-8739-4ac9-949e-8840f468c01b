<?php
require_once __DIR__ . '/../db/db.php';
require_once __DIR__ . '/../middleware/auth.php';
session_start();

// Prüfe, ob ein gültiger Access-Token vorliegt (z.B. aus User-Tabelle)
$org_id = $_SESSION['organization_id'] ?? null;
if (!$org_id) { echo "Nicht eingeloggt!"; exit; }

// Beispiel: Hole Access-Token für diese Orga aus Users-Tabelle (anpassen, falls du Access-Token woanders speicherst!)
$stmt = $pdo->prepare("SELECT * FROM users WHERE organization_id = ? ORDER BY created_at DESC LIMIT 1");
$stmt->execute([$org_id]);
$user = $stmt->fetch();
if (!$user || !$user['access_token']) {
    echo "Kein Access-Token gefunden!";
    exit;
}
$access_token = $accessToken;

// Helper-Funktion für API-Calls
function bexioApiCall($url, $token) {
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $token,
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $resp = curl_exec($ch);
    $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $type = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    curl_close($ch);
    return ['status'=>$status, 'type'=>$type, 'body'=>$resp];
}

// Firmenprofil abfragen
$profile = bexioApiCall('https://api.bexio.com/3.0/company_profile', $access_token);

$mwst = bexioApiCall('https://api.bexio.com/3.0/taxes', $access_token);

$contacts = bexioApiCall('https://api.bexio.com/3.0/contact', $access_token);

// Zeige die Ergebnisse an
$title = "bexio API Test";
ob_start();
?>

<h1>bexio API Test</h1>
<h2>Firmenprofil (<code>/company_profile</code>)</h2>
<pre><?= htmlentities($profile['body']) ?></pre>

<h2>Firmen MWST (<code>/taxes</code>)</h2>
<pre><?php print_r($mwst); ?></pre>

<h2>Firmen Contacts(<code>/contact</code>)</h2>
<pre><?php print_r($contacts); ?></pre>



<?php
$content = ob_get_clean();
require __DIR__ . '/layout.php';
?>
