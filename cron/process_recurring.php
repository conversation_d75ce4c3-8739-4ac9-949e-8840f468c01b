<?php
ignore_user_abort(true);
set_time_limit(300);

require_once __DIR__ . '/../db/db.php';

function processRecurringInvoices() {
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM organizations WHERE subscription_status = 'active'");
    $stmt->execute();
    $organizations = $stmt->fetchAll();

    foreach ($organizations as $org) {
        $stmt2 = $pdo->prepare("SELECT * FROM recurring_templates WHERE organization_id = ?");
        $stmt2->execute([$org['id']]);
        $templates = $stmt2->fetchAll();

        foreach ($templates as $template) {
            $nextDate = $template['last_executed'] ?? $template['start_date'];
            $nextDate = new DateTime($nextDate);
            $intervalSpec = convertIntervalStringToDateInterval($template['interval_str']);
            if (!$intervalSpec) continue;
            $nextDate->add($intervalSpec);
            $now = new DateTime();
            if ($now >= $nextDate) {
                createInvoiceFromTemplate($org, $template, $nextDate->format('Y-m-d'));
                $update = $pdo->prepare("UPDATE recurring_templates SET last_executed = ? WHERE id = ?");
                $update->execute([$nextDate->format('Y-m-d'), $template['id']]);
            }
        }
    }
}

function convertIntervalStringToDateInterval($intervalStr) {
    $intervalStr = strtolower(trim($intervalStr));
    switch ($intervalStr) {
        case '+1 month': return new DateInterval('P1M');
        case '+3 months': return new DateInterval('P3M');
        case '+6 months': return new DateInterval('P6M');
        case '+1 year': return new DateInterval('P1Y');
        default: return false;
    }
}

function replaceTemplateVariables($text, $dateStr) {
    $dt = new DateTime($dateStr);
    $map = [
        '{monat}' => $dt->format('F'),
        '{jahr}' => $dt->format('Y'),
        '{nächstermonat}' => $dt->modify('+1 month')->format('F'),
        '{nächstesjahr}' => $dt->modify('+1 year')->format('Y'),
    ];
    return strtr($text, $map);
}

function createInvoiceFromTemplate($org, $template, $invoiceDate) {
    global $pdo;

    $stmt = $pdo->prepare("SELECT * FROM users WHERE organization_id = ? AND is_admin = 1 LIMIT 1");
    $stmt->execute([$org['id']]);
    $user = $stmt->fetch();
    if (!$user) { echo "Kein Admin-User für Org {$org['id']} gefunden
"; return; }
    $title = replaceTemplateVariables($template['title'], $invoiceDate);
    $positions = json_decode($template['positions'], true);
    foreach ($positions as &$pos) $pos['text'] = replaceTemplateVariables($pos['text'], $invoiceDate);

    $invoiceData = [
        'contact_id' => $template['contact_id'],
        'title' => $title,
        'positions' => $positions,
        'date' => $invoiceDate,
    ];
    if ($org['subscription_status'] === 'inactive') {
        echo "Organisation {$org['name']} ist inaktiv - keine Rechnung erstellt
";
        return;
    }
    $ch = curl_init("https://api.bexio.com/3.0/kb_invoice");
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_HTTPHEADER => [
            "Authorization: Bearer {$user['access_token']}",
            "Content-Type: application/json"
        ],
        CURLOPT_POSTFIELDS => json_encode($invoiceData)
    ]);
    $result = curl_exec($ch);
    $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    if ($status == 201 || $status == 200) {
        echo "Rechnung für Org {$org['name']} am $invoiceDate erstellt.
";
    } else {
        echo "Fehler beim Erstellen der Rechnung für Org {$org['name']}: $result
";
    }
}
processRecurringInvoices();
?>