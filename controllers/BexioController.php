<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once __DIR__ . '/../db/db.php';

function bexioRequest($user, $method, $path, $body = null) {
    $url = "https://api.bexio.com/$path";
    $headers = [
        "Authorization: Bearer " . $user['access_token'],
        "Accept: application/json",
        "Content-Type: application/json"
    ];

    $ch = curl_init($url);
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_CUSTOMREQUEST => $method,
        CURLOPT_HTTPHEADER => $headers
    ]);

    if ($body !== null) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($body));
    }

    $response = curl_exec($ch);
    $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    header('Content-Type: application/json');
    http_response_code($status);
    echo $response;
}

function getBusinessData() {
    $user = requireLoginAndFreshToken();
    bexioRequest($user, "GET", "2.0/company");
}
function getContacts() {
    $user = requireLoginAndFreshToken();
    bexioRequest($user, "GET", "3.0/contact");
}
function getContact() {
    $user = requireLoginAndFreshToken();
    $data = json_decode(file_get_contents("php://input"), true);
    $id = $data['id'] ?? 0;
    bexioRequest($user, "GET", "3.0/contact/$id");
}
function getUnits() {
    $user = requireLoginAndFreshToken();
    bexioRequest($user, "GET", "2.0/units");
}
function getInvoices() {
    $user = requireLoginAndFreshToken();
    bexioRequest($user, "GET", "3.0/kb_invoice");
}
function getTaxes() {
    $user = requireLoginAndFreshToken();
    bexioRequest($user, "GET", "2.0/tax");
}
function getCurrencies() {
    $user = requireLoginAndFreshToken();
    bexioRequest($user, "GET", "2.0/currency");
}
function createCustomer() {
    $user = requireLoginAndFreshToken();
    $data = json_decode(file_get_contents("php://input"), true);
    bexioRequest($user, "POST", "3.0/contact", $data);
}
function createInvoice() {
    $user = requireLoginAndFreshToken();
    $data = json_decode(file_get_contents("php://input"), true);
    bexioRequest($user, "POST", "3.0/kb_invoice", $data);
}
function updateInvoice() {
    $user = requireLoginAndFreshToken();
    $data = json_decode(file_get_contents("php://input"), true);
    $id = $data['id'] ?? 0;
    bexioRequest($user, "POST", "3.0/kb_invoice/$id", $data);
}
function restartRecurringBilling() {
    $user = requireLoginAndFreshToken();
    $data = json_decode(file_get_contents("php://input"), true);
    $id = $data['id'] ?? 0;
    bexioRequest($user, "POST", "3.0/recurring_bill/$id/restart");
}
function deleteRecurringBilling() {
    $user = requireLoginAndFreshToken();
    $data = json_decode(file_get_contents("php://input"), true);
    $id = $data['id'] ?? 0;
    bexioRequest($user, "DELETE", "3.0/recurring_bill/$id");
}
function pauseRecurringBilling() {
    $user = requireLoginAndFreshToken();
    $data = json_decode(file_get_contents("php://input"), true);
    $id = $data['id'] ?? 0;
    bexioRequest($user, "POST", "3.0/recurring_bill/$id/pause");
}
?>